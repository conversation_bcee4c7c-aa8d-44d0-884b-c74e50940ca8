/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const greet: () => void;
export const __wbg_contourtracer_free: (a: number, b: number) => void;
export const contourtracer_new: (a: number, b: number) => number;
export const contourtracer_set_pixels: (a: number, b: number, c: number) => void;
export const contourtracer_extract_contours: (a: number, b: number) => number;
export const __wbindgen_malloc: (a: number, b: number) => number;
